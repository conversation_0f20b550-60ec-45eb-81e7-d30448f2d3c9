//挂载一个对象到window上
/**
 * @param {Object} option
 * @param {String} option.container 挂载的容器
 * @param {Object} option.iframeStyle iframe的样式
 * @param {Object} option.controls 控制器
 */
;(function (global) {
  let iframe = document.createElement("iframe");
  window.addEventListener("message", receiveMessage, false);
  let styleElement = null;
  // 默认的iframe样式配置
  // 默认的iframe样式配置
  const defaultIframeStyles = {
    position: "fixed",
    width: "392px",
    height: "632px",
    bottom: "32px",
    right: "-408px",
    zIndex: "99",
    transition: "right 0.3s ease-in-out",
    border: "none",
    // borderRadius: "8px",
    // boxShadow: "0px 2px 14px 0px rgba(137, 137, 140, 0.15)",
  };

  // 移动端样式配置
  const mobileIframeStyles = {
    width: "100%",
    height: "100%",
    bottom: "0",
    right: "-100%",
  };

  // 低高度屏幕样式配置
  const lowHeightStyles = {
    bottom: "0",
    height: "100%",
  };

  // 创建并注入CSS样式
  function injectIframeStyles(customStyles = {}) {
    // 如果已存在样式元素，先移除
    if (styleElement) {
      document.head.removeChild(styleElement);
    }

    // 合并自定义样式
    const finalStyles = { ...defaultIframeStyles, ...customStyles };

    // 生成CSS规则
    const cssRules = [];

    // 基础样式
    const baseStyleProps = Object.entries(finalStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`.fsLiveChat { ${baseStyleProps}; }`);

    // 显示状态样式
    cssRules.push(`.fsLiveChat.show { right: 68px; opacity: 1; }`);

    // 移动端媒体查询
    const mobileStyleProps = Object.entries(mobileIframeStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`
      @media (max-width: 768px) {
        .fsLiveChat {
          ${mobileStyleProps};
        }
        .fsLiveChat.show {
          right: 0;
          z-index: 99999;
        }
      }
    `);

    // 低高度屏幕媒体查询
    const lowHeightStyleProps = Object.entries(lowHeightStyles)
      .map(([key, value]) => `${camelToKebab(key)}: ${value}`)
      .join("; ");

    cssRules.push(`
      @media (max-height: 700px) {
        .fsLiveChat {
          ${lowHeightStyleProps};
        }
      }
    `);

    // 创建style元素并注入
    styleElement = document.createElement("style");
    styleElement.type = "text/css";
    styleElement.setAttribute("data-livechat-styles", "true");
    styleElement.textContent = cssRules.join("\n");
    document.head.appendChild(styleElement);
  }

  // 驼峰转短横线命名
  function camelToKebab(str) {
    return str.replace(/([A-Z])/g, "-$1").toLowerCase();
  }


  // 处理接收到的消息
  function receiveMessage(event) {
    if (event.data === "hide_chat") {
      document.querySelector("iframe.fsLiveChat").className = "fsLiveChat";
    }
    if (event.data?.type === "open_chat") {
      document.querySelector("iframe.fsLiveChat").className = "fsLiveChat show";
    }
  }
  function getDooringApiStr(opt) {
    let controls = Object.assign(
      {
        //按照实际需求新增
      },
      opt || {}
    );
    if (["ru"].includes(controls["webSite"])) {
      controls["webSite"] = "en";
      controls["isoCode"] = "US";
      controls["language"] = 5;
    }
    let params = "";
    for (let key in controls) {
      params += key + "=" + encodeURI(controls[key]) + "&";
    }
    return params.slice(0, params.length - 1);
  }

  global.fsLiveChatMount = function (option) {
    this.option = option;
    // const sdk_domain_path = '$_path' //项目地址
    const sdk_domain_path = "http://10.28.86.17:5174"; //项目地址
    
    // 注入iframe样式
    const customStyles = option && option.iframeStyle ? option.iframeStyle : {};
    // injectIframeStyles(customStyles);

    iframe.src = sdk_domain_path + "?" + getDooringApiStr(option);
    iframe.allow = "geolocation";
    iframe.style.border = "none";
    iframe.className = "fsLiveChat";
    iframe.title = "FsLiveChat";
    

    document.querySelector(option.container || "body").appendChild(iframe);
  };
  //卸载
  global.fsLiveChatUnmount = function () {
    document.querySelector(this.option.container || "body").removeChild(iframe);
    window.removeEventListener("message", receiveMessage, false);
    // 清理注入的样式
    if (styleElement && document.head.contains(styleElement)) {
      document.head.removeChild(styleElement);
      styleElement = null;
    }
  };
  //显示
  global.showFsLiveChat = function () {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage(
        { type: "open_chat", origin: window.location.href },
        "*"
      );
    document.querySelector("iframe.fsLiveChat").className = "fsLiveChat show";
  };
  //隐藏
  global.hideFsLiveChat = function () {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage("hide_chat", "*");
    document.querySelector("iframe.fsLiveChat").className = "fsLiveChat";
  };
  //
  global.postDataToChat = function (payload) {
    document
      .querySelector("iframe.fsLiveChat")
      .contentWindow.postMessage(
        { type: payload.type, data: payload.data },
        "*"
      );
  };
})(window)
