export default {
  popupTitle: "オンラインチャット",
  placeholder: "ここにテキストを入力...",
  fileType: "ファイル/画像",
  chatNow: "今すぐチャットする",
  endChat: "チャット終了？",
  closeTips: "チャットを終了しますか？",
  closeBtn: "閉じる",
  feedbackTitle: "会話の内容を評価してください。",
  good: "良い",
  bad: "悪い",
  rateTip: "評価した",
  hasVoted: "お客様投票にご協力いただきありがとうございます。",
  commentTitle: "何かご意見はありますか？",
  ratePlaceholder: "コメントを入力してください...",
  submit: "送信",
  read: "既読",
  unread: "未読",
  emailTranscript: "この内容をメールで送信する",
  mute: "ミュート",
  unmute: "ミュート解除",
  emailTip: "この会話記録を送信するためのメールアドレスをご入力ください。",
  sendBtn: "メール送信",
  sendStatus: "メール送信",
  sendInfoStart: "この会話記録は、",
  sendInfoEnd:
    "に送信されました。数分以内にメールが届かない場合は、迷惑メールフォルダをご確認ください。",
  backChat: "会話に戻る",
  vaildInfo: "有効なメールアドレスではありません。",
  message: "メッセージ$a件",
  downloadApp: "FSアプリをダウンロードする",
  more3Files: "最大3ファイルまでアップロード可能です。",
  fileSize5M: "ファイルの最大サイズは5Mです。",
  fileSize20M: "ファイルの最大サイズは20Mです。",
  invalidFileType: "無効なファイルタイプ",
  robot: "仮想アシスタント",
  advisor: "Fsアドバイザー",
  typing: "エージェントが入力中...",
  newFeedback: {
    title: "会話を評価",
    comment: "コメント（こめんと）",
    update: "アップデート",
  },
  userForm: {
    name: "名前",
    email: "メールアドレス",
    selectAgent: "エージェントを選択",
    selectOption: ["製品サポート", "テクニカルサポート"],
    saved: "保存済み",
  },
  placeholder2: "返信...",
  viewPastConversations: "過去の会話を見る",
  conversationHistory: "会話履歴",
  videoTip: {
    title: "動画を見る",
    text: "新しいウィンドウで動画を再生しますか？",
    btn: "今すぐ開始",
  },
  visitorUser: "訪問ユーザー",
  privacyPolicy: {
    acceptText: "承諾を選択することにより、FS.com, Inc.、その関連会社、およびサービスプロバイダーがチャット会話の内容を記録、保存、共有、分析することに明示的に同意したことになります。詳細については、<a href=\"https://www.fs.com/policies/privacy_notice.html\" target=\"_blank\">プライバシーポリシーおよび収集に関する通知</a>をご確認ください。",
    rejectText: "大丈夫です。いつでもポリシーを確認し、続行に同意することができます",
    acceptBtn: "はい、同意します。",
    rejectBtn: "いいえ、今はしません。"
  },
};
